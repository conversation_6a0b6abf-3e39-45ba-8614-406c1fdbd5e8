allprojects {
    repositories {
        google()
        mavenCentral()
        // 华为仓库 - 用于华为推送服务
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
