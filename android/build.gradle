group = "com.cq.jiguang_push_plugin"
version = "1.0"

buildscript {
    repositories {
        google()
        mavenCentral()
        // 华为push
        maven { url 'https://developer.huawei.com/repo/' }
    }

    dependencies {
        classpath("com.android.tools.build:gradle:8.1.0")
        // 华为push
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
        // 华为push
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

apply plugin: "com.android.library"
// 华为push
apply plugin: 'com.huawei.agconnect'

android {
    namespace = "com.cq.jiguang_push_plugin"

    compileSdk = 35

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    defaultConfig {
        minSdk = 21
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }

    dependencies {
        testImplementation("junit:junit:4.13.2")
        testImplementation("org.mockito:mockito-core:5.0.0")
        // 极光推送依赖
        implementation 'cn.jiguang.sdk:jpush:5.8.0'
        // 小米推送依赖
        // https://mvnrepository.com/artifact/cn.jiguang.sdk.plugin/xiaomi
        implementation 'cn.jiguang.sdk.plugin:xiaomi:5.8.0'
        // 华为推送依赖
        // https://mvnrepository.com/artifact/cn.jiguang.sdk.plugin/huawei
        implementation 'cn.jiguang.sdk.plugin:huawei:5.8.0'
        implementation 'com.huawei.hms:push:6.12.0.300'
//        implementation 'com.huawei.agconnect:agconnect-core:1.8.1.300'
        // vivo推送依赖
        // https://mvnrepository.com/artifact/cn.jiguang.sdk.plugin/vivo
        implementation 'cn.jiguang.sdk.plugin:vivo:5.8.0'
        // oppo推送依赖 - start
        // https://mvnrepository.com/artifact/cn.jiguang.sdk.plugin/oppo
        implementation 'cn.jiguang.sdk.plugin:oppo:5.8.0'
        // 注意：OPPO AAR 依赖需要在使用插件的项目中配置，插件本身不能依赖AAR
        //OPPO 3.1.0 aar 及其以上版本需要添加以下依赖
        implementation 'com.google.code.gson:gson:2.11.0'
        implementation 'commons-codec:commons-codec:1.11'
        implementation 'androidx.annotation:annotation:1.9.1'
        // oppo推送依赖 - end
    }

    sourceSets {
        main {
            assets.srcDirs += ['src/main/assets', 'assets'] // ✅ 增加 assets 路径
        }
    }



    testOptions {
        unitTests.all {
            testLogging {
                events "passed", "skipped", "failed", "standardOut", "standardError"
                outputs.upToDateWhen { false }
                showStandardStreams = true
            }
        }
    }
}
