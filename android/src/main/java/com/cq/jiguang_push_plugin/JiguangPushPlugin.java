package com.cq.jiguang_push_plugin;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.NonNull;


import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.PluginRegistry;

/**
 * JiguangPushPlugin
 */
public class JiguangPushPlugin implements FlutterPlugin, ActivityAware, PluginRegistry.NewIntentListener {
    private static final String TAG = "JiguangPushPlugin";
    private static final String PUSH_ACTION = "com.changqing.health";
    private static final String JPUSH_ACTION = "cn.jpush.android.intent.JNotifyActivity";

    /// The MethodChannel that will the communication between Flutter and native Android
    ///
    /// This local reference serves to register the plugin with the Flutter Engine and unregister it
    /// when the Flutter Engine is detached from the Activity
    private MethodChannel channel;
    private PushManager pushManager;

    private ActivityPluginBinding activityBinding;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        Context context = flutterPluginBinding.getApplicationContext();
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), "jiguang_push_plugin");

        // 创建消息处理器
        MethodCallDispatcher dispatcher = new MethodCallDispatcher(context);
        channel.setMethodCallHandler(dispatcher);

        // 初始化推送管理器
        pushManager = PushManager.getInstance();
//        pushManager.init(context);

        Log.d(TAG, "JiguangPushPlugin attached to engine");
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
        if (pushManager != null) {
            pushManager.destroy();
        }
        Log.d(TAG, "JiguangPushPlugin detached from engine");
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activityBinding = binding;
        Activity activity = binding.getActivity();
        Intent intent = activity.getIntent();
        Log.e(TAG, "onAttachedToActivity: " + intent.getAction());
        if (PUSH_ACTION.equals(intent.getAction())) {
            // TODO 处理推送意图
            handlePushIntent(intent);
        }
        binding.addOnNewIntentListener(this);
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        if (activityBinding != null) {
            activityBinding.removeOnNewIntentListener(this);
        }
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activityBinding = binding;
        binding.addOnNewIntentListener(this);
    }

    @Override
    public void onDetachedFromActivity() {
        if (activityBinding != null) {
            activityBinding.removeOnNewIntentListener(this);
            activityBinding = null;
        }
    }

    /**
     * 处理新的意图
     *
     * @param intent The new intent that was started for the activity.
     * @return
     */
    @Override
    public boolean onNewIntent(@NonNull Intent intent) {
        Log.e(TAG, "onNewIntent: " + intent.getAction());
        if (PUSH_ACTION.equals(intent.getAction())) {
            handlePushIntent(intent);
        }
        return true;
    }

    /**
     * 处理推送意图
     *
     * @param intent
     */
    private void handlePushIntent(Intent intent) {
        // TODO 处理推送意图，推送消息到 Dart
        String value1 = intent.getStringExtra("key1");
        int value2 = intent.getIntExtra("key2", 0);
        Log.e(TAG, "handlePushIntent: value1: " + value1 + ", value2: " + value2);
    }
}
