package com.cq.jiguang_push_plugin;

import android.content.Context;
import android.util.Log;

import cn.jpush.android.api.CmdMessage;
import cn.jpush.android.api.CustomMessage;
import cn.jpush.android.api.JPushMessage;
import cn.jpush.android.api.NotificationMessage;
import cn.jpush.android.service.JPushMessageReceiver;

/**
 * 极光推送消息接收器
 * 继承JPushMessageReceiver，处理各种推送消息和事件
 */
public class PushMessageReceiver extends JPushMessageReceiver {
    private static final String TAG = "PushMessageReceiver";

    /**
     * 收到通知消息时回调
     */
    @Override
    public void onMessage(Context context, CustomMessage customMessage) {
        Log.d(TAG, "Received custom message: " + customMessage.toString());
        // 可以在这里处理自定义消息
        // 例如：发送到Flutter层处理
    }

    /**
     * 收到通知时回调
     */
    @Override
    public void onNotifyMessageArrived(Context context, NotificationMessage message) {
        Log.d(TAG, "Notification message arrived: " + message.toString());
        // 通知到达时的处理逻辑
    }

    /**
     * 点击通知时回调
     */
    @Override
    public void onNotifyMessageOpened(Context context, NotificationMessage message) {
        Log.d(TAG, "Notification message opened: " + message.toString());
        // 点击通知时的处理逻辑
        // 可以在这里启动特定的Activity或发送事件到Flutter层
    }

    /**
     * 通知被删除时回调
     */
    @Override
    public void onNotifyMessageDismiss(Context context, NotificationMessage message) {
        Log.d(TAG, "Notification message dismissed: " + message.toString());
        // 通知被删除时的处理逻辑
    }

    /**
     * 注册成功时回调
     */
    @Override
    public void onRegister(Context context, String registrationId) {
        Log.d(TAG, "JPush registration successful, registrationId: " + registrationId);
        // 注册成功后的处理逻辑
        // 可以将registrationId发送到服务器或Flutter层
    }

    /**
     * 连接状态变化时回调
     */
    @Override
    public void onConnected(Context context, boolean isConnected) {
        Log.d(TAG, "JPush connection status changed: " + isConnected);
        // 连接状态变化时的处理逻辑
    }

    /**
     * 命令消息回调
     */
    @Override
    public void onCommandResult(Context context, CmdMessage cmdMessage) {
        Log.d(TAG, "Command result: " + cmdMessage.toString());
        // 处理命令执行结果
        // 例如：设置别名、标签的结果
    }

    /**
     * 标签操作结果回调
     */
    @Override
    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
        Log.d(TAG, "Tag operator result: " + jPushMessage.toString());
        // 处理标签操作结果
    }

    /**
     * 检查标签操作结果回调
     */
    @Override
    public void onCheckTagOperatorResult(Context context, JPushMessage jPushMessage) {
        Log.d(TAG, "Check tag operator result: " + jPushMessage.toString());
        // 处理检查标签操作结果
    }

    /**
     * 别名操作结果回调
     */
    @Override
    public void onAliasOperatorResult(Context context, JPushMessage jPushMessage) {
        Log.d(TAG, "Alias operator result: " + jPushMessage.toString());
        // 处理别名操作结果
    }

    /**
     * 手机号码操作结果回调
     */
    @Override
    public void onMobileNumberOperatorResult(Context context, JPushMessage jPushMessage) {
        Log.d(TAG, "Mobile number operator result: " + jPushMessage.toString());
        // 处理手机号码操作结果
    }
}
