package com.cq.jiguang_push_plugin;

import android.content.Context;
import java.util.HashSet;
import java.util.Set;

/**
 * PushManager 使用示例
 * 展示如何使用 PushManager 进行推送相关操作
 */
public class PushManagerExample {
    
    /**
     * 初始化推送示例
     */
    public static void initializePushExample(Context context) {
        // 获取PushManager实例
        PushManager pushManager = PushManager.getInstance();
        
        // 初始化推送
        pushManager.init(context);
        
        // 获取注册ID
        String registrationId = pushManager.getRegistrationId();
        System.out.println("Registration ID: " + registrationId);
    }
    
    /**
     * 设置别名示例
     */
    public static void setAliasExample() {
        PushManager pushManager = PushManager.getInstance();
        
        // 设置别名
        String alias = "user123";
        int sequence = 1;
        pushManager.setAlias(alias, sequence);
    }
    
    /**
     * 设置标签示例
     */
    public static void setTagsExample() {
        PushManager pushManager = PushManager.getInstance();
        
        // 创建标签集合
        Set<String> tags = new HashSet<>();
        tags.add("vip");
        tags.add("android");
        tags.add("news");
        
        // 设置标签
        int sequence = 2;
        pushManager.setTags(tags, sequence);
    }
    
    /**
     * 添加标签示例
     */
    public static void addTagsExample() {
        PushManager pushManager = PushManager.getInstance();
        
        // 创建要添加的标签集合
        Set<String> newTags = new HashSet<>();
        newTags.add("sports");
        newTags.add("music");
        
        // 添加标签
        int sequence = 3;
        pushManager.addTags(newTags, sequence);
    }
    
    /**
     * 删除标签示例
     */
    public static void deleteTagsExample() {
        PushManager pushManager = PushManager.getInstance();
        
        // 创建要删除的标签集合
        Set<String> tagsToDelete = new HashSet<>();
        tagsToDelete.add("news");
        
        // 删除标签
        int sequence = 4;
        pushManager.deleteTags(tagsToDelete, sequence);
    }
    
    /**
     * 推送服务控制示例
     */
    public static void pushServiceControlExample() {
        PushManager pushManager = PushManager.getInstance();
        
        // 检查推送服务状态
        boolean isStopped = pushManager.isPushStopped();
        System.out.println("Push service stopped: " + isStopped);
        
        if (!isStopped) {
            // 停止推送服务
            pushManager.stopPush();
            System.out.println("Push service stopped");
        } else {
            // 恢复推送服务
            pushManager.resumePush();
            System.out.println("Push service resumed");
        }
    }
    
    /**
     * 完整的推送初始化流程示例
     */
    public static void completeInitializationExample(Context context) {
        PushManager pushManager = PushManager.getInstance();
        
        // 1. 初始化推送
        pushManager.init(context);
        
        // 2. 等待一段时间确保初始化完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        
        // 3. 获取注册ID
        String registrationId = pushManager.getRegistrationId();
        if (!registrationId.isEmpty()) {
            System.out.println("Push initialized successfully, Registration ID: " + registrationId);
            
            // 4. 设置用户别名
            pushManager.setAlias("user_" + System.currentTimeMillis(), 1);
            
            // 5. 设置用户标签
            Set<String> userTags = new HashSet<>();
            userTags.add("android_user");
            userTags.add("active_user");
            pushManager.setTags(userTags, 2);
            
        } else {
            System.out.println("Push initialization may not be complete yet");
        }
    }
}
