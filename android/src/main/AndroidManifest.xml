<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.cq.jiguang_push_plugin">

    <!-- Required -->
    <permission
        android:name="${applicationId}.permission.JPUSH_MESSAGE"
        android:protectionLevel="signature" />
    <!-- Required  一些系统要求的权限，如访问网络等 -->
    <uses-permission android:name="${applicationId}.permission.JPUSH_MESSAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!--huawei_permission_start-->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <!--huawei_permission_end-->
    <!--vivo_permission_start-->
    <uses-permission android:name="com.vivo.notification.permission.BADGE_ICON" />
    <!--vivo_permission_end-->
    <!-- Optional for location -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!--honor_permission_start-->
    <uses-permission android:name="com.hihonor.android.launcher.permission.CHANGE_BADGE" />
    <queries>
        <intent>
            <action android:name="com.hihonor.push.action.BIND_PUSH_SERVICE" />
        </intent>
    </queries>
    <!--honor_permission_end-->
    <!--xiaomi_permission_start-->
    <uses-permission android:name="android.permission.VIBRATE" />
    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />
    <uses-permission android:name="${applicationId}.permission.MIPUSH_RECEIVE" />
    <!--xiaomi_permission_end-->
    <!--oppo_permission_start-->
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.heytap.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <!--oppo_permission_end-->
    <!--meizu_permission_start-->
    <uses-permission android:name="com.meizu.flyme.permission.PUSH" />
    <!--meizu_permission_end-->


    <application>
        <!-- Since JCore2.0.0 Required SDK核心功能-->
        <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
        <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
        <service
            android:name=".PushCommonService"
            android:enabled="true"
            android:exported="false"
            android:process=":pushcore">
            <intent-filter>
                <action android:name="cn.jiguang.user.service.action" />
            </intent-filter>
        </service>

        <!-- Required since 3.0.7 -->
        <!-- 新的 tag/alias 接口结果返回需要开发者配置一个自定的广播 -->
        <!-- 3.3.0开始所有事件将通过该类回调 -->
        <!-- 该广播需要继承 JPush 提供的 JPushMessageReceiver 类, 并如下新增一个 Intent-Filter -->
        <receiver
            android:name=".PushMessageReceiver"
            android:enabled="true"
            android:exported="false">
            <intent-filter>
                <!-- 从 JPush v5.4.0 版本开始，请特别注意 RECEIVER_MESSAGE 写法, 如配置错误则无法找到回调类 -->
                <action android:name="cn.jpush.android.intent.RECEIVER_MESSAGE" />
                <category android:name="com.changqing.health" />
            </intent-filter>
        </receiver>

    </application>
</manifest>
