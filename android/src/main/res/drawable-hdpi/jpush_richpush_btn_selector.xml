<?xml version="1.0" encoding="utf-8"?> 
<selector xmlns:android="http://schemas.android.com/apk/res/android" > 
    <!-- 获得焦点但未按下时的背景图片 --> 
    <item 
        android:state_focused="true" 
        android:state_enabled="true" 
        android:state_pressed="false" 
        android:drawable="@drawable/jpush_ic_richpush_actionbar_back" />
     <!-- 按下时的背景图片 --> 
    <item 
        android:state_enabled="true" 
        android:state_pressed="true" 
        android:drawable="@android:color/darker_gray" /> 
    <!-- 按下时的背景图片 --> 
    <item 
        android:state_enabled="true" 
        android:state_checked="true" 
        android:drawable="@android:color/darker_gray" /> 
    <!-- 默认时的背景图片 --> 
    <item android:drawable="@drawable/jpush_ic_richpush_actionbar_back" />
</selector> 